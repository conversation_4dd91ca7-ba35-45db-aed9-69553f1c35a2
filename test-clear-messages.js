// Simple test script to verify the clear messages API behavior
// This can be run in the browser console to test the functionality

async function testClearMessagesAPI() {
  try {
    console.log('Testing clear messages API...');
    
    const response = await fetch('/api/clear-messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Clear messages response:', data);

    // Check if this is a temporary user response
    if (data.shouldRedirect && data.newChatId) {
      console.log('✅ Temporary user detected - new chat ID provided:', data.newChatId);
      console.log('✅ Should redirect to new chat');
    } else {
      console.log('✅ Authenticated user - no redirect needed');
    }

    return data;
  } catch (error) {
    console.error('❌ Error testing clear messages API:', error);
    return null;
  }
}

// Run the test
testClearMessagesAPI();
